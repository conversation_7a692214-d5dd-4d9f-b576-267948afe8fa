import gorilla
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch_scatter
from scipy.optimize import linear_sum_assignment
from typing import Optional

def box_iou_3d(boxes1, boxes2):
    """
    计算3D包围盒的IoU（交并比），假设为AABB，不考虑旋转。
    输入:
        boxes1: [N, 6] (cx, cy, cz, l, w, h)
        boxes2: [M, 6] (cx, cy, cz, l, w, h)
    输出:
        iou: [N, M]
    """
    # [N, 1, 6] 和 [1, M, 6] 方便广播
    boxes1 = boxes1[:, None, :]  # [N, 1, 6]
    boxes2 = boxes2[None, :, :]  # [1, M, 6]

    # 取 min/max 坐标
    boxes1_min = boxes1[..., :3] - boxes1[..., 3:] / 2
    boxes1_max = boxes1[..., :3] + boxes1[..., 3:] / 2
    boxes2_min = boxes2[..., :3] - boxes2[..., 3:] / 2
    boxes2_max = boxes2[..., :3] + boxes2[..., 3:] / 2

    # 交集框
    inter_min = torch.maximum(boxes1_min, boxes2_min)
    inter_max = torch.minimum(boxes1_max, boxes2_max)
    inter_size = torch.clamp(inter_max - inter_min, min=0)
    inter_volume = inter_size[..., 0] * inter_size[..., 1] * inter_size[..., 2]

    # 体积
    vol1 = (boxes1_max - boxes1_min).prod(-1)
    vol2 = (boxes2_max - boxes2_min).prod(-1)

    # 并集
    union = vol1 + vol2 - inter_volume

    iou = inter_volume / (union + 1e-6)
    return iou  # [N, M]

def box_giou_3d(boxes1, boxes2):
    """
    计算3D包围盒的GIoU（广义交并比）
    输入:
        boxes1: [N, 6] (cx, cy, cz, l, w, h)
        boxes2: [M, 6] (cx, cy, cz, l, w, h)
    输出:
        giou: [N, M]
    """
    # [N, 1, 6] 和 [1, M, 6] 方便广播
    boxes1 = boxes1[:, None, :]  # [N, 1, 6]
    boxes2 = boxes2[None, :, :]  # [1, M, 6]

    # 取 min/max 坐标
    boxes1_min = boxes1[..., :3] - boxes1[..., 3:] / 2
    boxes1_max = boxes1[..., :3] + boxes1[..., 3:] / 2
    boxes2_min = boxes2[..., :3] - boxes2[..., 3:] / 2
    boxes2_max = boxes2[..., :3] + boxes2[..., 3:] / 2

    # 交集框
    inter_min = torch.maximum(boxes1_min, boxes2_min)
    inter_max = torch.minimum(boxes1_max, boxes2_max)
    inter_size = torch.clamp(inter_max - inter_min, min=0)
    inter_volume = inter_size[..., 0] * inter_size[..., 1] * inter_size[..., 2]

    # 体积
    vol1 = (boxes1_max - boxes1_min).prod(-1)
    vol2 = (boxes2_max - boxes2_min).prod(-1)

    # 并集
    union = vol1 + vol2 - inter_volume
    
    # IoU
    iou = inter_volume / (union + 1e-6)
    
    # 计算最小外接框（enclosing box）
    enclosing_min = torch.minimum(boxes1_min, boxes2_min)
    enclosing_max = torch.maximum(boxes1_max, boxes2_max)
    enclosing_size = enclosing_max - enclosing_min
    enclosing_volume = enclosing_size[..., 0] * enclosing_size[..., 1] * enclosing_size[..., 2]
    
    # GIoU = IoU - (enclosing_volume - union) / enclosing_volume
    giou = iou - (enclosing_volume - union) / (enclosing_volume + 1e-6)
    
    return giou  # [N, M]

def iou_loss(pred_bboxes, tgt_bboxes):
    """Compute IoU loss using simple 3D IoU"""
    # 只使用前6维进行IoU计算 (cx, cy, cz, l, w, h)
    pred_boxes_6d = pred_bboxes[:, :6]
    tgt_boxes_6d = tgt_bboxes[:, :6]
    # iou_matrix = box_iou_3d(pred_boxes_6d, tgt_boxes_6d)
    iou_matrix = box_giou_3d(pred_boxes_6d, tgt_boxes_6d) # 使用giou试一下
    iou = iou_matrix.diag()
    return 1 - iou.mean()

@torch.jit.script
def batch_sigmoid_bce_loss(inputs: torch.Tensor, targets: torch.Tensor):
    """
    Args:
        inputs: (num_querys, N)
        targets: (num_inst, N)
    Returns:
        Loss tensor
    """
    N = inputs.shape[1]

    pos = F.binary_cross_entropy_with_logits(inputs, torch.ones_like(inputs), reduction='none')
    neg = F.binary_cross_entropy_with_logits(inputs, torch.zeros_like(inputs), reduction='none')

    loss = torch.einsum('nc,mc->nm', pos, targets) + torch.einsum('nc,mc->nm', neg, (1 - targets))

    return loss / N

@torch.jit.script
def batch_dice_loss(inputs: torch.Tensor, targets: torch.Tensor):
    """Compute batch DICE loss for masks"""
    inputs = inputs.sigmoid()
    numerator = 2 * torch.einsum('nc,mc->nm', inputs, targets)
    denominator = inputs.sum(-1)[:, None] + targets.sum(-1)[None, :]
    loss = 1 - (numerator + 1) / (denominator + 1)
    return loss

def get_iou(inputs: torch.Tensor, targets: torch.Tensor):
    """Compute IoU score"""
    inputs = inputs.sigmoid()
    binarized_inputs = (inputs >= 0.5).float()
    targets = (targets > 0.5).float()
    intersection = (binarized_inputs * targets).sum(-1)
    union = targets.sum(-1) + binarized_inputs.sum(-1) - intersection
    score = intersection / (union + 1e-6)
    return score

@torch.jit.script
def dice_loss(inputs: torch.Tensor, targets: torch.Tensor):
    """Compute DICE loss"""
    inputs = inputs.sigmoid()
    numerator = 2 * (inputs * targets).sum(-1)
    denominator = inputs.sum(-1) + targets.sum(-1)
    loss = 1 - (numerator + 1) / (denominator + 1)
    return loss.mean()

class HungarianMatcher(nn.Module):
    def __init__(self, cost_weight):
        super().__init__()
        self.register_buffer('cost_weight', torch.tensor(cost_weight))
        
    @torch.no_grad()
    def forward(self, pred_labels, pred_masks, pred_bboxes, insts):
        indices = []
        
        for pred_label, pred_mask, pred_bbox, inst in zip(pred_labels, pred_masks, pred_bboxes, insts):
            if len(inst) == 0:
                indices.append(([], []))
                continue
            pred_label = pred_label.softmax(-1)  # (n_q, 19)
            tgt_idx = inst.gt_labels  # (num_inst,)
            cost_class = -pred_label[:, tgt_idx]
            tgt_mask = inst.gt_spmasks
            tgt_mask = tgt_mask.to(pred_mask.device)
            cost_mask = batch_sigmoid_bce_loss(pred_mask, tgt_mask.float())
            cost_dice = batch_dice_loss(pred_mask, tgt_mask.float())
            
            # 使用GIOU进行匹配，替代中心点距离
            pred_bbox_6d = pred_bbox[:, :6]  # (cx, cy, cz, l, w, h)
            gt_bboxes_6d = inst.gt_bboxes[:, :6].to(pred_bbox.device)  # (cx, cy, cz, l, w, h)
            giou_matrix = box_giou_3d(pred_bbox_6d, gt_bboxes_6d)  # [num_queries, num_gt]
            cost_giou = -giou_matrix  # 负号因为匈牙利算法寻找最小成本，而我们希望最大化GIOU

            C_class = cost_class
            C_mask = cost_dice
            C_box = cost_giou  # 使用GIOU代替中心点距离
            
            C = (self.cost_weight[0] * C_class + 
                 self.cost_weight[1] * C_mask + 
                 self.cost_weight[2] * C_box)
            C = C.cpu()
            indices.append(linear_sum_assignment(C))
        return [(torch.as_tensor(i, dtype=torch.int64), torch.as_tensor(j, dtype=torch.int64)) for i, j in indices]
    
    
@gorilla.LOSSES.register_module()
class Criterion(nn.Module):
    def __init__(
        self,
        ignore_label=-100,
        loss_weight=[1.0, 1.0, 1.0, 1.0, 1.0, 0.1],
        cost_weight=[1.0, 1.0, 1.0],
        non_object_weight=0.1,
        num_class=40,
        latent_dim=256,
        z_weight=0.1,
        bbox_weights=None,
        sem_weight=1.0,
        geo_weight=0.5,
    ):
        super().__init__()
        class_weight = torch.ones(num_class + 1)
        class_weight[-1] = non_object_weight
        self.register_buffer('class_weight', class_weight)
        
        loss_weight = torch.tensor(loss_weight)
        self.register_buffer('loss_weight', loss_weight)
        
        self.matcher = HungarianMatcher(cost_weight)
        
        self.num_class = num_class
        self.ignore_label = ignore_label
        
        self.latent_dim = latent_dim
        self.z_weight = z_weight
        
        if bbox_weights is None:
            bbox_weights = {'center': 2.0, 'size': 1.0, 'angle': 0.5, 'giou': 3.0}
        
        self.register_buffer('bbox_center_weight', torch.tensor(bbox_weights['center']))
        self.register_buffer('bbox_size_weight', torch.tensor(bbox_weights['size']))
        self.register_buffer('bbox_angle_weight', torch.tensor(bbox_weights['angle']))
        self.register_buffer('bbox_giou_weight', torch.tensor(bbox_weights.get('giou', 2.0)))

        self.register_buffer('sem_weight', torch.tensor(sem_weight))
        self.register_buffer('geo_weight', torch.tensor(geo_weight))
    
    def _get_src_permutation_idx(self, indices):
        batch_idx = torch.cat([torch.full_like(src, i) for i, (src, _) in enumerate(indices)])
        src_idx = torch.cat([src for (src, _) in indices])
        return batch_idx, src_idx
    
    def get_inst_info(self, batched_gt_instance, coords, batch_offsets):
        for i, gt_inst in enumerate(batched_gt_instance):
            start_id = batch_offsets[i]
            end_id = batch_offsets[i + 1]
            coord = coords[start_id:end_id]
            inst_idx, point_idx = torch.nonzero(gt_inst['gt_masks'], as_tuple=True)
            inst_point = coord[point_idx]
            gt_inst['gt_center'] = torch_scatter.segment_coo(inst_point, inst_idx.cuda(), reduce='mean')
    
    def get_layer_loss(self, layer, aux_outputs, insts):
        loss_out = {}
        pred_labels = aux_outputs['labels']
        pred_scores = aux_outputs['scores']
        pred_masks = aux_outputs['masks']
        pred_bboxes = aux_outputs['bboxes']
        pred_zs = aux_outputs.get('zs', None)
        
        device = pred_labels.device
        for inst in insts:
            inst.gt_labels = inst.gt_labels.to(device)
            inst.gt_spmasks = inst.gt_spmasks.to(device)
            inst.gt_bboxes = inst.gt_bboxes.to(device)
        indices = self.matcher(pred_labels, pred_masks, pred_bboxes, insts)
        idx = self._get_src_permutation_idx(indices)
        

        tgt_class_o = torch.cat([inst.gt_labels[idx_gt] for inst, (_, idx_gt) in zip(insts, indices)])
        tgt_class = torch.full(
            pred_labels.shape[:2],
            self.num_class,
            dtype=torch.int64,
            device=pred_labels.device,
        )
        tgt_class[idx] = tgt_class_o
        class_loss = F.cross_entropy(pred_labels.transpose(1, 2), tgt_class, self.class_weight)
        loss_out['cls_loss'] = class_loss.item()
        

        score_loss = torch.tensor([0.0], device=pred_labels.device)
        

        tgt_bboxes = torch.cat([inst.gt_bboxes[idx_gt] for inst, (_, idx_gt) in zip(insts, indices)])

        tgt_bbox_valid = torch.cat([inst.gt_bbox_valid[idx_gt] if hasattr(inst, 'gt_bbox_valid') else torch.ones(len(idx_gt), dtype=torch.bool) for inst, (_, idx_gt) in zip(insts, indices)])

        if tgt_bboxes.shape[0] > 0:
            tgt_bboxes = tgt_bboxes.to(pred_bboxes[0].device)
            tgt_bbox_valid = tgt_bbox_valid.to(pred_bboxes[0].device)
            

            pred_bboxes_matched = []
            for i, (batch_idx, query_idx) in enumerate(zip(idx[0], idx[1])):
                pred_bboxes_matched.append(pred_bboxes[batch_idx][query_idx])
            pred_bboxes_matched = torch.stack(pred_bboxes_matched)
            
            
            center_loss = F.huber_loss(pred_bboxes_matched[:, :3], tgt_bboxes[:, :3], delta=1.0)

            pred_size = torch.abs(pred_bboxes_matched[:, 3:6]) + 1e-6
            tgt_size = torch.abs(tgt_bboxes[:, 3:6]) + 1e-6
            size_loss = F.huber_loss(pred_size, tgt_size, delta=0.5)
            if pred_bboxes_matched.shape[1] == 30:
                if tgt_bbox_valid.any():
                    valid_angle_cls_logits = pred_bboxes_matched[tgt_bbox_valid, 6:18]
                    valid_angle_reg_pred = pred_bboxes_matched[tgt_bbox_valid, 18:30]
                    valid_tgt_angles = tgt_bboxes[tgt_bbox_valid, 6]
                    
                    from utils.util.bbox import BBoxUtils
                    bbox_utils = BBoxUtils(num_heading_bin=12)
                    
                    gt_angle_cls = []
                    gt_angle_reg = []
                    
                    for gt_angle in valid_tgt_angles:
                        cls_id, residual = bbox_utils.angle2class(gt_angle.cpu().numpy())
                        gt_angle_cls.append(cls_id)
                        gt_angle_reg.append(residual)
                    
                    gt_angle_cls = torch.tensor(gt_angle_cls, device=pred_bboxes_matched.device, dtype=torch.long)
                    gt_angle_reg = torch.tensor(gt_angle_reg, device=pred_bboxes_matched.device, dtype=torch.float32)
                    
                    angle_cls_loss = F.cross_entropy(valid_angle_cls_logits, gt_angle_cls)
                    
                    angle_reg_selected = valid_angle_reg_pred[torch.arange(len(gt_angle_cls)), gt_angle_cls]
                    angle_reg_loss = F.huber_loss(angle_reg_selected, gt_angle_reg, delta=0.5)
                    
                    angle_loss = angle_cls_loss + angle_reg_loss
                else:
                    angle_cls_loss = torch.tensor(0.0, device=pred_bboxes_matched.device)
                    angle_reg_loss = torch.tensor(0.0, device=pred_bboxes_matched.device)
                    angle_loss = torch.tensor(0.0, device=pred_bboxes_matched.device)
                
            else:
                if tgt_bbox_valid.any():
                    valid_pred_angles = pred_bboxes_matched[tgt_bbox_valid, 6]
                    valid_tgt_angles = tgt_bboxes[tgt_bbox_valid, 6]
                    angle_diff = valid_pred_angles - valid_tgt_angles
                    angle_diff = torch.atan2(torch.sin(angle_diff), torch.cos(angle_diff))
                    angle_loss = F.huber_loss(angle_diff, torch.zeros_like(angle_diff), delta=0.5)
                else:
                    angle_loss = torch.tensor(0.0, device=pred_bboxes_matched.device)
            # 保留IoU loss计算
            iou_loss_val = iou_loss(pred_bboxes_matched, tgt_bboxes)
            

            loss_out['center_loss'] = center_loss.item()
            loss_out['size_loss'] = size_loss.item()
            loss_out['iou_loss'] = iou_loss_val.item()
            

            if pred_bboxes_matched.shape[1] == 30:
                loss_out['angle_cls_loss'] = angle_cls_loss.item()
                loss_out['angle_reg_loss'] = angle_reg_loss.item()
                loss_out['angle_loss'] = angle_loss.item()
            else:
                loss_out['angle_loss'] = angle_loss.item()
            # 恢复完整的bbox loss计算，包含IoU权重
            bbox_loss = (self.bbox_center_weight * center_loss +
                        self.bbox_size_weight * size_loss +
                        self.bbox_angle_weight * angle_loss +
                        self.bbox_giou_weight * iou_loss_val)
        else:
            bbox_loss = torch.tensor(0.0, device=pred_labels.device)

        loss_out['bbox_loss'] = bbox_loss.item()
        

        mask_bce_loss = torch.tensor([0.0], device=pred_labels.device)
        mask_dice_loss = torch.tensor([0.0], device=pred_labels.device)
        for i, (mask, score, inst, (idx_q, idx_gt)) in enumerate(zip(pred_masks, pred_scores, insts, indices)):
            if len(inst) == 0:
                continue
            pred_score = score[idx_q]
            pred_mask = mask[idx_q]  # (num_inst, N)
            tgt_mask = inst.gt_spmasks[idx_gt]  # (num_inst, N)

            tgt_mask = tgt_mask.to(pred_mask.device)
            
            # 🔧 修复：使用bbox IoU作为score的监督信号，而不是mask IoU
            with torch.no_grad():
                # 获取对应的bbox预测和GT
                pred_bbox = pred_bboxes[i][idx_q]  # 匹配的预测bbox
                tgt_bbox = inst.gt_bboxes[idx_gt].to(pred_bbox.device)  # 对应的GT bbox
                
                if len(pred_bbox) > 0 and len(tgt_bbox) > 0:
                    # 只使用前6维进行IoU计算 (cx, cy, cz, l, w, h)
                    pred_bbox_6d = pred_bbox[:, :6]
                    tgt_bbox_6d = tgt_bbox[:, :6]
                    
                    # 计算bbox GIoU作为score目标
                    iou_matrix = box_giou_3d(pred_bbox_6d, tgt_bbox_6d)
                    tgt_score = iou_matrix.diag().unsqueeze(1)  # 每个预测与对应GT的IoU
                else:
                    # 如果没有有效bbox，回退到mask IoU
                    tgt_score = get_iou(pred_mask, tgt_mask).unsqueeze(1)

            filter_id, _ = torch.where(tgt_score > 0.3)
            if filter_id.numel():
                tgt_score = tgt_score[filter_id]
                pred_score = pred_score[filter_id]
                score_loss += F.mse_loss(pred_score, tgt_score)
            mask_bce_loss += F.binary_cross_entropy_with_logits(pred_mask, tgt_mask.float())
            mask_dice_loss += dice_loss(pred_mask, tgt_mask.float())
        score_loss = score_loss / len(pred_masks)
        mask_bce_loss = mask_bce_loss / len(pred_masks)
        mask_dice_loss = mask_dice_loss / len(pred_masks)
        
        loss_out['score_loss'] = score_loss.item()
        loss_out['mask_bce_loss'] = mask_bce_loss.item()
        loss_out['mask_dice_loss'] = mask_dice_loss.item()
        

        zs_loss = torch.tensor(0.0, device=pred_labels.device)
        if pred_zs is not None:

            pred_zs_matched = []
            tgt_zs_matched = []
            
            for batch_idx, (inst, (idx_q, idx_gt)) in enumerate(zip(insts, indices)):
                if len(idx_q) > 0 and len(idx_gt) > 0 and hasattr(inst, 'gt_zs') and inst.gt_zs is not None:
                    if pred_zs.dim() == 3:  # [B, num_query, latent_dim]
                        pred_z_selected = pred_zs[batch_idx][idx_q]
                    else:
                        continue

                    # 添加有效边界框过滤，与主层保持一致
                    valid_mask = inst.gt_bbox_valid[idx_gt] if hasattr(inst, 'gt_bbox_valid') else torch.ones(len(idx_gt), dtype=torch.bool)
                    valid_indices = idx_gt[valid_mask]
                    
                    if len(valid_indices) > 0:
                        pred_z_valid = pred_z_selected[valid_mask]
                        gt_z_raw = inst.gt_zs[valid_indices]
                        if isinstance(gt_z_raw, np.ndarray):
                            gt_z_selected = torch.from_numpy(gt_z_raw.copy()).float().to(pred_zs.device)
                        else:
                            gt_z_selected = gt_z_raw.float().to(pred_zs.device)
                        pred_zs_matched.append(pred_z_valid)
                        tgt_zs_matched.append(gt_z_selected)
            
            if len(pred_zs_matched) > 0:
                pred_zs_all = torch.cat(pred_zs_matched, dim=0)  # [total_matched, latent_dim]
                tgt_zs_all = torch.cat(tgt_zs_matched, dim=0)    # [total_matched, latent_dim]
                
                # 使用Huber loss替代MSE (更鲁棒，与主层一致)
                z_diff = pred_zs_all - tgt_zs_all  # [total_matched, latent_dim]
                z_loss_per_sample = F.huber_loss(z_diff, torch.zeros_like(z_diff), delta=1.0, reduction='none').mean(dim=1)  # [total_matched]
                
                zs_loss = z_loss_per_sample.mean()
        
        loss_out['zs_loss'] = zs_loss.item()

        L_sem = (self.loss_weight[0] * class_loss + 
                self.loss_weight[1] * mask_bce_loss + 
                self.loss_weight[2] * mask_dice_loss)
            

        L_geo = (self.loss_weight[3] * bbox_loss + 
                self.loss_weight[5] * zs_loss)
            

        loss = (self.sem_weight * L_sem + 
                self.geo_weight * L_geo + 
                self.loss_weight[4] * score_loss)  # score权重保持原有配置
            

        loss_out['L_sem'] = L_sem.item()
        loss_out['L_geo'] = L_geo.item()
        loss_out['L_total_sem'] = (self.sem_weight * L_sem).item()
        loss_out['L_total_geo'] = (self.geo_weight * L_geo).item()


        return loss, loss_out
    
    def forward(self, pred, insts, gt_zs=None, pred_zs=None):
        loss_out = {}
        
        pred_labels = pred['labels']
        pred_scores = pred['scores']
        pred_masks = pred['masks']
        pred_bboxes = pred['bboxes']
        
        device = pred_labels.device
        for inst in insts:
            inst.gt_labels = inst.gt_labels.to(device)
            inst.gt_spmasks = inst.gt_spmasks.to(device)
            inst.gt_bboxes = inst.gt_bboxes.to(device)
        
        indices = self.matcher(pred_labels, pred_masks, pred_bboxes, insts)
        idx = self._get_src_permutation_idx(indices)
        
        tgt_class_o = torch.cat([inst.gt_labels[idx_gt] for inst, (_, idx_gt) in zip(insts, indices)])
        tgt_class = torch.full(
            pred_labels.shape[:2],
            self.num_class,
            dtype=torch.int64,
            device=pred_labels.device,
        )  # (B, num_query)
        tgt_class[idx] = tgt_class_o
        class_loss = F.cross_entropy(pred_labels.transpose(1, 2), tgt_class, self.class_weight)

        loss_out['cls_loss'] = class_loss.item()

        # score loss
        score_loss = torch.tensor([0.0], device=pred_labels.device)

        tgt_bboxes = torch.cat([inst.gt_bboxes[idx_gt] for inst, (_, idx_gt) in zip(insts, indices)])
        tgt_bbox_valid = torch.cat([inst.gt_bbox_valid[idx_gt] if hasattr(inst, 'gt_bbox_valid') else torch.ones(len(idx_gt), dtype=torch.bool) for inst, (_, idx_gt) in zip(insts, indices)])
        
        if tgt_bboxes.shape[0] > 0:
            tgt_bboxes = tgt_bboxes.to(pred_bboxes[0].device)
            tgt_bbox_valid = tgt_bbox_valid.to(pred_bboxes[0].device)
            
            # 正确提取匹配的预测bbox
            pred_bboxes_matched = []
            for i, (batch_idx, query_idx) in enumerate(zip(idx[0], idx[1])):
                pred_bboxes_matched.append(pred_bboxes[batch_idx][query_idx])
            pred_bboxes_matched = torch.stack(pred_bboxes_matched)

            # 所有匹配的实例都计算中心和尺寸loss
            center_loss = F.huber_loss(pred_bboxes_matched[:, :3], tgt_bboxes[:, :3], delta=1.0)

            pred_size = torch.abs(pred_bboxes_matched[:, 3:6]) + 1e-6
            tgt_size = torch.abs(tgt_bboxes[:, 3:6]) + 1e-6
            size_loss = F.huber_loss(pred_size, tgt_size, delta=0.5)

            # 旋转角loss只对有效bbox计算
            if pred_bboxes_matched.shape[1] == 30:
                # 30维格式：角度分类+回归
                if tgt_bbox_valid.any():
                    valid_angle_cls_logits = pred_bboxes_matched[tgt_bbox_valid, 6:18]
                    valid_angle_reg_pred = pred_bboxes_matched[tgt_bbox_valid, 18:30]
                    valid_tgt_angles = tgt_bboxes[tgt_bbox_valid, 6]
                    
                    from utils.util.bbox import BBoxUtils
                    bbox_utils = BBoxUtils(num_heading_bin=12)
                    
                    gt_angle_cls = []
                    gt_angle_reg = []
                    
                    for gt_angle in valid_tgt_angles:
                        cls_id, residual = bbox_utils.angle2class(gt_angle.cpu().numpy())
                        gt_angle_cls.append(cls_id)
                        gt_angle_reg.append(residual)
                    
                    gt_angle_cls = torch.tensor(gt_angle_cls, device=pred_bboxes_matched.device, dtype=torch.long)
                    gt_angle_reg = torch.tensor(gt_angle_reg, device=pred_bboxes_matched.device, dtype=torch.float32)
                    
                    angle_cls_loss = F.cross_entropy(valid_angle_cls_logits, gt_angle_cls)
                    angle_reg_selected = valid_angle_reg_pred[torch.arange(len(gt_angle_cls)), gt_angle_cls]
                    angle_reg_loss = F.huber_loss(angle_reg_selected, gt_angle_reg, delta=0.5)
                    
                    angle_loss = angle_cls_loss + angle_reg_loss
                else:
                    angle_cls_loss = torch.tensor(0.0, device=pred_bboxes_matched.device)
                    angle_reg_loss = torch.tensor(0.0, device=pred_bboxes_matched.device)
                    angle_loss = torch.tensor(0.0, device=pred_bboxes_matched.device)
            else:
                # 7维格式：直接角度回归，只对有效bbox计算
                if tgt_bbox_valid.any():
                    valid_pred_angles = pred_bboxes_matched[tgt_bbox_valid, 6]
                    valid_tgt_angles = tgt_bboxes[tgt_bbox_valid, 6]
                    angle_diff = valid_pred_angles - valid_tgt_angles
                    # 处理角度的周期性 (-π, π)
                    angle_diff = torch.atan2(torch.sin(angle_diff), torch.cos(angle_diff))
                    angle_loss = F.huber_loss(angle_diff, torch.zeros_like(angle_diff), delta=0.5)
                else:
                    angle_loss = torch.tensor(0.0, device=pred_labels.device)

            # 保留IoU loss计算
            iou_loss_val = iou_loss(pred_bboxes_matched, tgt_bboxes)
            
            loss_out['center_loss'] = center_loss.item()
            loss_out['size_loss'] = size_loss.item()
            loss_out['iou_loss'] = iou_loss_val.item()
            
            if pred_bboxes_matched.shape[1] == 30:
                loss_out['angle_cls_loss'] = angle_cls_loss.item()
                loss_out['angle_reg_loss'] = angle_reg_loss.item()
                loss_out['angle_loss'] = angle_loss.item()
            else:
                loss_out['angle_loss'] = angle_loss.item()

            # 恢复完整的bbox loss计算，包含IoU权重
            bbox_loss = (self.bbox_center_weight * center_loss +
                        self.bbox_size_weight * size_loss +
                        self.bbox_angle_weight * angle_loss +
                        self.bbox_giou_weight * iou_loss_val)
        else:
            bbox_loss = torch.tensor(0.0, device=pred_labels.device)

        loss_out['bbox_loss'] = bbox_loss.item()

        # mask loss
        mask_bce_loss = torch.tensor([0.0], device=pred_labels.device)
        mask_dice_loss = torch.tensor([0.0], device=pred_labels.device)
        for i, (mask, score, inst, (idx_q, idx_gt)) in enumerate(zip(pred_masks, pred_scores, insts, indices)):
            if len(inst) == 0:
                continue
            pred_score = score[idx_q]
            pred_mask = mask[idx_q]  # (num_inst, N)
            tgt_mask = inst.gt_spmasks[idx_gt]  # (num_inst, N)
            # 确保设备匹配
            tgt_mask = tgt_mask.to(pred_mask.device)
            
            # 🔧 修复：使用bbox IoU作为score的监督信号，而不是mask IoU
            with torch.no_grad():
                # 获取对应的bbox预测和GT
                pred_bbox = pred_bboxes[i][idx_q]  # 匹配的预测bbox
                tgt_bbox = inst.gt_bboxes[idx_gt].to(pred_bbox.device)  # 对应的GT bbox
                
                if len(pred_bbox) > 0 and len(tgt_bbox) > 0:
                    # 只使用前6维进行IoU计算 (cx, cy, cz, l, w, h)
                    pred_bbox_6d = pred_bbox[:, :6]
                    tgt_bbox_6d = tgt_bbox[:, :6]
                    
                    # 计算bbox GIoU作为score目标
                    iou_matrix = box_giou_3d(pred_bbox_6d, tgt_bbox_6d)
                    tgt_score = iou_matrix.diag().unsqueeze(1)  # 每个预测与对应GT的IoU
                else:
                    # 如果没有有效bbox，回退到mask IoU
                    tgt_score = get_iou(pred_mask, tgt_mask).unsqueeze(1)

            filter_id, _ = torch.where(tgt_score > 0.5)
            if filter_id.numel():
                tgt_score = tgt_score[filter_id]
                pred_score = pred_score[filter_id]
                score_loss += F.mse_loss(pred_score, tgt_score)
            mask_bce_loss += F.binary_cross_entropy_with_logits(pred_mask, tgt_mask.float())
            mask_dice_loss += dice_loss(pred_mask, tgt_mask.float())
        score_loss = score_loss / len(pred_masks)
        mask_bce_loss = mask_bce_loss / len(pred_masks)
        mask_dice_loss = mask_dice_loss / len(pred_masks)
        
        loss_out['score_loss'] = score_loss.item()
        loss_out['mask_bce_loss'] = mask_bce_loss.item()
        loss_out['mask_dice_loss'] = mask_dice_loss.item()
        
        zs_loss = torch.tensor(0.0, device=pred_labels.device)
        if pred_zs is not None or (pred.get('zs') is not None):
            # 优先使用pred字典中的zs，否则使用传入的pred_zs
            if pred.get('zs') is not None:
                pred_zs_tensor = pred['zs']
            elif pred_zs is not None:
                pred_zs_tensor = pred_zs
            else:
                pred_zs_tensor = None
                
            if pred_zs_tensor is not None:
                pred_zs_matched = []
                tgt_zs_matched = []
                
                for batch_idx, (inst, (idx_q, idx_gt)) in enumerate(zip(insts, indices)):
                    if len(idx_q) > 0 and len(idx_gt) > 0:
                        if pred_zs_tensor.dim() == 3:
                            pred_z_selected = pred_zs_tensor[batch_idx][idx_q]
                        elif isinstance(pred_zs_tensor, list) and batch_idx < len(pred_zs_tensor):
                            pred_z_selected = pred_zs_tensor[batch_idx][idx_q]
                        else:
                            continue
                        if hasattr(inst, 'gt_zs') and inst.gt_zs is not None and len(inst.gt_zs) > 0:
                            valid_mask = inst.gt_bbox_valid[idx_gt] if hasattr(inst, 'gt_bbox_valid') else torch.ones(len(idx_gt), dtype=torch.bool)
                            valid_indices = idx_gt[valid_mask]
                            
                            if len(valid_indices) > 0:
                                pred_z_valid = pred_z_selected[valid_mask]
                                gt_z_raw = inst.gt_zs[valid_indices]
                                if isinstance(gt_z_raw, np.ndarray):
                                    gt_z_selected = torch.from_numpy(gt_z_raw.copy()).float().to(pred_z_valid.device)
                                else:
                                    gt_z_selected = gt_z_raw.float().to(pred_z_valid.device)
                                pred_zs_matched.append(pred_z_valid)
                                tgt_zs_matched.append(gt_z_selected)
                        elif gt_zs is not None and batch_idx < len(gt_zs) and gt_zs[batch_idx] is not None:
                            gt_z_batch = gt_zs[batch_idx]
                            if len(gt_z_batch) > max(idx_gt):
                                gt_z_raw = gt_z_batch[idx_gt]
                                if isinstance(gt_z_raw, np.ndarray):
                                    gt_z_selected = torch.from_numpy(gt_z_raw.copy()).float().to(pred_z_selected.device)
                                else:
                                    gt_z_selected = gt_z_raw.float().to(pred_z_selected.device)
                                pred_zs_matched.append(pred_z_selected)
                                tgt_zs_matched.append(gt_z_selected)
                if len(pred_zs_matched) > 0:
                    pred_zs_all = torch.cat(pred_zs_matched, dim=0)  # [total_matched, latent_dim]
                    tgt_zs_all = torch.cat(tgt_zs_matched, dim=0)    # [total_matched, latent_dim]
                    
                    # 🔧 使用Huber loss替代MSE (更鲁棒，与DIMR一致)
                    z_diff = pred_zs_all - tgt_zs_all  # [total_matched, latent_dim]
                    z_loss_per_sample = F.huber_loss(z_diff, torch.zeros_like(z_diff), delta=1.0, reduction='none').mean(dim=1)  # [total_matched]
                    
                    zs_loss = z_loss_per_sample.mean()
        
        loss_out['zs_loss'] = zs_loss.item()
        
        L_sem = (self.loss_weight[0] * class_loss + 
                self.loss_weight[1] * mask_bce_loss + 
                self.loss_weight[2] * mask_dice_loss)

        L_geo = (self.loss_weight[3] * bbox_loss + 
                self.loss_weight[5] * zs_loss)
            
        loss = (self.sem_weight * L_sem + 
                self.geo_weight * L_geo + 
                self.loss_weight[4] * score_loss)
        loss_out['L_sem'] = L_sem.item()
        loss_out['L_geo'] = L_geo.item()
        loss_out['L_total_sem'] = (self.sem_weight * L_sem).item()
        loss_out['L_total_geo'] = (self.geo_weight * L_geo).item()

        if 'aux_outputs' in pred:
            for i, aux_outputs in enumerate(pred['aux_outputs']):
                loss_i, loss_out_i = self.get_layer_loss(i, aux_outputs, insts)
                loss += loss_i

        loss_out['loss'] = loss.item()
        return loss, loss_out