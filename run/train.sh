#!/bin/bash
# MAFT Training with Wandb Integration
# 使用示例脚本
set -e  # 遇到错误立即退出
echo "🚀 启动MAFT训练 (集成wandb)"
echo "================================"
# 检查依赖
echo "📋 检查依赖..."
python -c "import wandb; print('✅ wandb已安装')" || {
    echo "❌ wandb未安装，正在安装..."
    pip install wandb>=0.15.0
}

python -c "import torch; print('✅ torch已安装，版本:', torch.__version__)" || {
    echo "❌ torch未安装，请安装PyTorch"
    exit 1
}

# 设置环境变量
export CUDA_VISIBLE_DEVICES=0  # 根据需要调整GPU
export PYTHONPATH=$PYTHONPATH:$(pwd)

# 实验配置
CONFIG_PATH="configs/timr_scannet.yaml"  # 配置文件路径
EXP_NAME="timr_$(date +%Y%m%d_%H%M%S)"  # 实验名称
WORLD_SIZE=1  # GPU数量
RESUME_PATH=""  # 恢复训练路径，留空表示从头开始

# 检查配置文件
if [ ! -f "$CONFIG_PATH" ]; then
    echo "❌ 配置文件不存在: $CONFIG_PATH"
    echo "请检查配置文件路径"
    exit 1
fi

echo "📝 训练配置:"
echo "   配置文件: $CONFIG_PATH"
echo "   实验名称: $EXP_NAME"
echo "   GPU数量: $WORLD_SIZE"
echo "   恢复路径: ${RESUME_PATH:-'从头开始'}"

# 确认开始
read -p "是否开始训练? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ 训练已取消"
    exit 1
fi

# 开始训练
echo "🎯 开始训练..."
echo "================================"

if [ -n "$RESUME_PATH" ]; then
    # 恢复训练
    python train_ddp.py \
        --config "$CONFIG_PATH" \
        --exp-name "$EXP_NAME" \
        --world-size "$WORLD_SIZE" \
        --resume "$RESUME_PATH"
else
    # 从头开始训练
    python train_ddp.py \
        --config "$CONFIG_PATH" \
        --exp-name "$EXP_NAME" \
        --world-size "$WORLD_SIZE"
fi

echo "🎉 训练完成!"
echo "📊 查看结果: https://wandb.ai/" 