#!/usr/bin/env python3
import argparse
import os
import sys
import time
import datetime
import logging
from pathlib import Path
import warnings
import torch
import torch.nn as nn
import torch.optim as optim
import torch.distributed as dist
from torch.nn.parallel import DistributedDataParallel as DDP
from torch.utils.data import DataLoader
from torch.utils.data.distributed import DistributedSampler
import numpy as np
import yaml
import wandb

def format_time(seconds):
    """格式化时间显示"""
    if seconds < 60:
        return f"{seconds:.0f}s"
    elif seconds < 3600:
        minutes = seconds // 60
        secs = seconds % 60
        return f"{minutes:.0f}m{secs:.0f}s"
    else:
        hours = seconds // 3600
        minutes = (seconds % 3600) // 60
        return f"{hours:.0f}h{minutes:.0f}m"

def get_gpu_memory():
    """获取GPU内存使用情况"""
    if torch.cuda.is_available():
        memory_allocated = torch.cuda.memory_allocated() / 1024**3
        memory_reserved = torch.cuda.memory_reserved() / 1024**3
        return memory_allocated, memory_reserved
    return 0, 0

def setup_distributed(rank, world_size, backend='nccl'):
    """初始化分布式训练环境"""
    os.environ['MASTER_ADDR'] = 'localhost'
    os.environ['MASTER_PORT'] = '12355'
    
    dist.init_process_group(backend, rank=rank, world_size=world_size)
    torch.cuda.set_device(rank)

def cleanup_distributed():
    """清理分布式训练环境"""
    dist.destroy_process_group()

def is_main_process():
    """判断是否为主进程"""
    return not dist.is_initialized() or dist.get_rank() == 0

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入模型和数据
from model.maft.model import MAFT
from data import build_dataset, build_dataloader
from utils.logger import get_logger


class MAFTTrainerDDP:
    """MAFT分布式训练器"""
    
    def __init__(self, config_path, exp_name=None, resume=None, rank=0, world_size=1):
        self.rank = rank
        self.world_size = world_size
        self.config_path = config_path  # 保存配置文件路径
        
        # 加载配置
        self.config = self._load_config(config_path)
        
        # 设置实验目录
        self.exp_name = exp_name or self.config.get('output', {}).get('exp_name', 'maft_exp')
        self.exp_dir = os.path.join('experiments', self.exp_name)
        if is_main_process():
            os.makedirs(self.exp_dir, exist_ok=True)
            print(f"Experiment directory: {self.exp_dir}")
        
        # 设置设备 (需要在wandb初始化之前)
        self.device = torch.device(f'cuda:{rank}')
        
        # 设置日志（只在主进程）
        if is_main_process():
            self.logger = get_logger(
                name='MAFT',
                log_file=os.path.join(self.exp_dir, 'train.log'),
                log_level=logging.INFO
            )
            
            self.logger.info(f"Using device: {self.device}, World size: {world_size}")
            
            # 初始化wandb (在device设置之后)
            self._init_wandb()
        else:
            self.logger = None
        
        # 设置随机种子
        self._set_random_seed(self.config['train']['seed'] + rank)
        
        # 初始化模型
        self._build_model()
        
        # 初始化数据加载器
        self._build_dataloader()
        
        # 初始化优化器和调度器
        self._build_optimizer()
        
        # 训练状态
        self.current_epoch = 0
        self.best_metric = 0.0  # 最高AP值
        self.best_val_loss = float('inf')  # 最低val loss
        self.global_step = 0  # 全局step计数器
        
        # 时间统计
        self.train_start_time = None
        self.epoch_start_time = None
        self.batch_times = []
        
        # 恢复训练
        if resume:
            self._resume_training(resume)
    
    def _load_config(self, config_path):
        """加载配置文件"""
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        return config
    
    def _set_random_seed(self, seed):
        """设置随机种子"""
        torch.manual_seed(seed)
        torch.cuda.manual_seed_all(seed)
        np.random.seed(seed)
        torch.backends.cudnn.deterministic = True
        torch.backends.cudnn.benchmark = False
    
    def _build_model(self):
        """构建模型"""
        if is_main_process():
            self.logger.info("Building MAFT model...")
        
        # 构建MAFT模型 - 按照QIMR的方式处理
        model_name = self.config['model'].pop("name", "MAFT")
        self.model = MAFT(**self.config['model'])
        self.model = self.model.to(self.device)
        
        # 加载预训练权重
        self._load_pretrained_weights()
        
        # 使用DistributedDataParallel包装模型
        if self.world_size > 1:
            self.model = DDP(
                self.model, 
                device_ids=[self.rank], 
                output_device=self.rank,
                find_unused_parameters=True  # 允许未使用的参数
            )
        
        # 模型参数统计（只在主进程）
        if is_main_process():
            raw_model = self.model.module if hasattr(self.model, 'module') else self.model
            total_params = sum(p.numel() for p in raw_model.parameters())
            trainable_params = sum(p.numel() for p in raw_model.parameters() if p.requires_grad)
            self.logger.info(f"Total parameters: {total_params:,}")
            self.logger.info(f"Trainable parameters: {trainable_params:,}")
            
            # 记录模型信息到wandb
            wandb.config.update({
                'total_parameters': total_params,
                'trainable_parameters': trainable_params,
                'model_size_mb': sum(p.numel() * p.element_size() for p in raw_model.parameters()) / 1024**2
            })
            
            # 尝试记录模型结构
            try:
                wandb.watch(raw_model, log="all", log_freq=1000, log_graph=True)
                self.logger.info("✅ Model architecture logged to wandb")
            except Exception as e:
                self.logger.warning(f"⚠️ Failed to log model to wandb: {e}")
    
    def _load_pretrained_weights(self):
        """加载预训练权重"""
        train_config = self.config['train']
        
        if train_config.get('pretrain') and is_main_process():
            self.logger.info(f"Loading MAFT pretrained weights from {train_config['pretrain']}")
            # TODO: 实际加载预训练权重的逻辑
    
    def _build_dataloader(self):
        """构建数据加载器"""
        if is_main_process():
            self.logger.info("Building data loaders...")
        
        # 训练数据
        train_dataset = build_dataset(self.config['data']['train'], self.logger if is_main_process() else None)
        
        dataloader_config = self.config['dataloader']['train'].copy()
        self.train_loader = build_dataloader(
            train_dataset, 
            training=True, 
            dist=(self.world_size > 1),
            **dataloader_config
        )
        
        # 验证数据
        val_dataset = build_dataset(self.config['data']['val'], self.logger if is_main_process() else None)
        
        val_dataloader_config = self.config['dataloader']['val'].copy()
        self.val_loader = build_dataloader(
            val_dataset,
            training=False,
            dist=(self.world_size > 1),
            **val_dataloader_config
        )
        
        if is_main_process():
            self.logger.info(f"Train samples: {len(train_dataset)}")
            self.logger.info(f"Val samples: {len(val_dataset)}")
            
            # 记录数据集信息到wandb
            wandb.config.update({
                'train_samples': len(train_dataset),
                'val_samples': len(val_dataset),
                'train_batches': len(self.train_loader),
                'val_batches': len(self.val_loader)
            })
    
    def _build_optimizer(self):
        """构建优化器和学习率调度器"""
        param_groups = self._get_param_groups()
        
        optimizer_config = self.config['optimizer']
        if optimizer_config['type'] == 'AdamW':
            self.optimizer = optim.AdamW(
                param_groups,
                lr=optimizer_config['lr'],
                weight_decay=optimizer_config['weight_decay']
            )
        elif optimizer_config['type'] == 'Adam':
            self.optimizer = optim.Adam(
                param_groups,
                lr=optimizer_config['lr'],
                weight_decay=optimizer_config['weight_decay']
            )
        else:
            raise ValueError(f"Unsupported optimizer: {optimizer_config['type']}")
        
        scheduler_config = self.config['lr_scheduler']
        if scheduler_config['type'] == 'PolyLR':
            self.scheduler = optim.lr_scheduler.PolynomialLR(
                self.optimizer,
                total_iters=scheduler_config['max_iters'],
                power=scheduler_config['power']
            )
        else:
            raise ValueError(f"Unsupported scheduler: {scheduler_config['type']}")
    
    def _get_param_groups(self):
        """获取参数分组"""
        optimizer_config = self.config['optimizer']
        
        # 简化：直接返回所有参数
        return list(self.model.parameters())
    
    def train(self):
        """主训练循环"""
        total_epochs = self.config['train']['epochs']
        strategy_config = self.config.get('training_strategy', {})
        
        if is_main_process():
            self.logger.info("=" * 80)
            self.logger.info("🚀 MAFT DISTRIBUTED TRAINING STARTED")
            self.logger.info("=" * 80)
            self.logger.info(f"📅 Start Time: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            self.logger.info(f"🏷️  Experiment: {self.exp_name}")
            self.logger.info(f"📊 Total Epochs: {total_epochs}")
            self.logger.info(f"💾 Devices: {self.world_size} x GPU")
            
            self.logger.info(f"📚 Train Samples: {len(self.train_loader.dataset):,}")
            self.logger.info(f"📚 Val Samples: {len(self.val_loader.dataset):,}")
            self.logger.info(f"🔢 Batch Size per GPU: {self.config['dataloader']['train']['batch_size']}")
            self.logger.info(f"🔢 Total Batch Size: {self.config['dataloader']['train']['batch_size'] * self.world_size}")
            
            mem_alloc, mem_reserved = get_gpu_memory()
            self.logger.info(f"💾 GPU Memory: {mem_alloc:.1f}GB / {mem_reserved:.1f}GB")
            self.logger.info("=" * 80)
        
        self.train_start_time = time.time()
        
        for epoch in range(self.current_epoch, total_epochs):
            self.current_epoch = epoch
            
            # 设置epoch for distributed sampler
            if self.world_size > 1 and hasattr(self.train_loader.sampler, 'set_epoch'):
                self.train_loader.sampler.set_epoch(epoch)
            
            train_metrics = self._train_epoch()
            
            val_metrics = None
            if (epoch + 1) % self.config['evaluation']['eval_interval'] == 0:
                val_metrics = self._validate_epoch()
                
                if is_main_process():
                    # 检查是否是最佳AP模型
                    if self._is_best_model(val_metrics):
                        self.best_metric = val_metrics.get('all_ap', 0.0)
                        self._save_checkpoint(is_best=True, eval_metrics=val_metrics)
                    
                    # 检查是否是最低val loss模型
                    if self._is_best_val_loss(val_metrics):
                        self.best_val_loss = val_metrics.get('val_loss', float('inf'))
                        self._save_best_val_loss_checkpoint(val_metrics)
            
            if is_main_process() and (epoch + 1) % self.config['train']['interval'] == 0:
                self._save_checkpoint(is_best=False)
            
            self.scheduler.step()
            
            if is_main_process():
                self._log_epoch_metrics(train_metrics, val_metrics)
        
        if is_main_process():
            # 保存最后一个epoch的模型为latest.pth
            self._save_latest_checkpoint()
            self.logger.info("Training completed!")
            self._create_training_summary()
            wandb.finish()
    
    def _train_epoch(self):
        """训练一个epoch"""
        self.model.train()
        self.epoch_start_time = time.time()
        
        total_loss = 0.0
        loss_components = {}
        num_batches = len(self.train_loader)
        
        for batch_idx, batch in enumerate(self.train_loader):
            batch_start_time = time.time()
            
            batch = self._move_batch_to_device(batch)
            
            self.optimizer.zero_grad()
            
            loss, loss_dict = self.model(batch, mode='loss')
            
            loss.backward()
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
            self.optimizer.step()
            
            total_loss += loss.item()
            
            if isinstance(loss_dict, dict):
                for key, value in loss_dict.items():
                    if key not in loss_components:
                        loss_components[key] = 0.0
                    if isinstance(value, torch.Tensor):
                        loss_components[key] += value.item()
                    else:
                        loss_components[key] += value
            
            batch_time = time.time() - batch_start_time
            self.batch_times.append(batch_time)
            
            if is_main_process() and batch_idx % 50 == 0:
                self._log_batch_progress(batch_idx, num_batches, loss.item(), loss_dict, batch_time)
        
        if self.world_size > 1:
            total_loss_tensor = torch.tensor(total_loss, device=self.device)
            dist.all_reduce(total_loss_tensor, op=dist.ReduceOp.SUM)
            avg_loss = total_loss_tensor.item() / (num_batches * self.world_size)
            
            avg_loss_components = {}
            for key, value in loss_components.items():
                value_tensor = torch.tensor(value, device=self.device)
                dist.all_reduce(value_tensor, op=dist.ReduceOp.SUM)
                avg_loss_components[key] = value_tensor.item() / (num_batches * self.world_size)
        else:
            avg_loss = total_loss / num_batches
            avg_loss_components = {k: v/num_batches for k, v in loss_components.items()}
        
        return {'loss': avg_loss, 'loss_components': avg_loss_components}
    
    def _log_batch_progress(self, batch_idx, num_batches, loss, loss_dict, batch_time):
        """记录详细的批次进度"""
        progress = (batch_idx + 1) / num_batches * 100
        current_lr = self.optimizer.param_groups[0]['lr']
        mem_alloc, mem_reserved = get_gpu_memory()
        
        # 时间估算
        if len(self.batch_times) > 10:
            avg_batch_time = np.mean(self.batch_times[-50:])  # 最近50个batch的平均时间
            remaining_batches = num_batches - batch_idx - 1
            eta_epoch = remaining_batches * avg_batch_time
            
            # 整体训练剩余时间估算
            total_epochs = self.config['train']['epochs']
            if self.current_epoch > 0:
                elapsed_epochs = self.current_epoch + progress / 100
                avg_epoch_time = (time.time() - self.train_start_time) / elapsed_epochs
                remaining_epochs = total_epochs - elapsed_epochs
                eta_total = remaining_epochs * avg_epoch_time
            else:
                eta_total = eta_epoch * total_epochs
        else:
            eta_epoch = 0
            eta_total = 0
        
        # 构建损失字符串
        loss_str = f"Loss: {loss:.4f}"
        if isinstance(loss_dict, dict) and loss_dict:
            loss_components = []
            for key, value in loss_dict.items():
                if isinstance(value, torch.Tensor):
                    loss_components.append(f"{key}: {value.item():.3f}")
                else:
                    loss_components.append(f"{key}: {value:.3f}")
            if loss_components:
                loss_str += f" ({', '.join(loss_components)})"
        
        self.logger.info(
            f"[E{self.current_epoch:03d}][{batch_idx:04d}/{num_batches:04d}] "
            f"{progress:5.1f}% | {loss_str} | "
            f"LR: {current_lr:.2e} | "
            f"Time: {batch_time:.2f}s | "
            f"GPU{self.rank}: {mem_alloc:.1f}GB"
        )
        
        # ETA信息 (每100个batch显示一次)
        if batch_idx % 100 == 0 and eta_total > 0:
            eta_epoch_str = format_time(eta_epoch)
            eta_total_str = format_time(eta_total)
            eta_finish = datetime.datetime.now() + datetime.timedelta(seconds=eta_total)
            
            self.logger.info(
                f"     ⏰ ETA: {eta_epoch_str} (epoch) | {eta_total_str} (total) | "
                f"Finish: {eta_finish.strftime('%m-%d %H:%M')}"
            )
        
        # 记录batch级别的数据到wandb (每50个batch记录一次)
        if batch_idx % 50 == 0:
            batch_metrics = {
                'batch/loss': loss,
                'batch/learning_rate': current_lr,
                'batch/batch_time': batch_time,
                'batch/gpu_memory_allocated': mem_alloc,
                'batch/gpu_memory_reserved': mem_reserved,
                'batch/epoch': self.current_epoch,
                'batch/progress': progress,
            }
            
            # 记录loss组件
            if isinstance(loss_dict, dict) and loss_dict:
                for key, value in loss_dict.items():
                    if isinstance(value, torch.Tensor):
                        batch_metrics[f'batch/loss_{key}'] = value.item()
                    else:
                        batch_metrics[f'batch/loss_{key}'] = value
            
            wandb.log(batch_metrics, step=self.global_step)
        
        # 更新全局step
        self.global_step += 1
    
    def _validate_epoch(self):
        """验证一个epoch"""
        if is_main_process():
            self.logger.info('Validation')
        
        self.model.eval()
        
        pred_insts, gt_insts = [], []
        total_loss = 0.0
        num_batches = len(self.val_loader)
        
        # 累积loss组件
        accumulated_loss_components = {}
        
        # 进度条只在主进程显示
        if is_main_process():
            from tqdm import tqdm
            progress_bar = tqdm(total=len(self.val_loader), desc="Validating")
        
        with torch.no_grad():
            for batch_idx, batch in enumerate(self.val_loader):
                batch = self._move_batch_to_device(batch)
                
                # 计算损失
                loss, loss_dict = self.model(batch, mode='loss')
                total_loss += loss.item()
                
                # 累积loss组件
                if isinstance(loss_dict, dict):
                    for key, value in loss_dict.items():
                        if isinstance(value, torch.Tensor):
                            value = value.item()
                        if key not in accumulated_loss_components:
                            accumulated_loss_components[key] = 0.0
                        accumulated_loss_components[key] += value
                
                # 获取预测结果用于AP评估
                try:
                    result = self.model(batch, mode='predict')
                    
                    pred_insts.append(result['pred_instances'])
                    gt_insts.append(result['gt_instances'])
                except Exception as e:
                    # 如果模型不支持predict模式，跳过AP评估
                    if is_main_process() and batch_idx == 0:
                        self.logger.warning(f"Model does not support 'predict' mode: {e}")
                    pred_insts, gt_insts = [], []
                    break
                
                if is_main_process():
                    progress_bar.update()
        
        if is_main_process():
            progress_bar.close()
        
        # 计算平均损失
        if self.world_size > 1:
            total_loss_tensor = torch.tensor(total_loss, device=self.device)
            dist.all_reduce(total_loss_tensor, op=dist.ReduceOp.SUM)
            avg_loss = total_loss_tensor.item() / (num_batches * self.world_size)
        else:
            avg_loss = total_loss / num_batches
        
        # 计算平均loss组件
        avg_loss_components = {}
        for key, value in accumulated_loss_components.items():
            avg_loss_components[key] = value / num_batches
        
        # AP评估（只在主进程进行）
        eval_res = {
            'val_loss': avg_loss, 
            'all_ap': 0.0, 
            'all_ap_50%': 0.0, 
            'all_ap_25%': 0.0,
            'loss_components': avg_loss_components if avg_loss_components else None
        }
        
        if is_main_process() and len(pred_insts) > 0:
            try:
                # 导入评估器
                from model.maft.evaluation import ScanNetEval
                from utils.consts import RFS_labels
                # 获取类别信息
                val_dataset = self.val_loader.dataset
                # if hasattr(val_dataset, 'CLASSES'):
                #     classes = val_dataset.CLASSES
                # elif hasattr(val_dataset, 'classes'):
                #     classes = val_dataset.classes
                # else:
                #     # 使用默认的ScanNet类别
                #     classes = ['cabinet', 'bed', 'chair', 'sofa', 'table', 'door', 'window', 
                #               'bookshelf', 'picture', 'counter', 'desk', 'curtain', 'refrigerator', 
                #               'shower curtain', 'toilet', 'sink', 'bathtub', 'other furniture']
                classes = ['wall', 'floor', 'cabinet', 'bed', 'chair', 'sofa', 'table', 'door', 'window', 'bookshelf', 
                           'picture', 'counter', 'desk', 'curtain', 'refridgerator', 'shower curtain', 'toilet', 'sink', 'bathtub', 
                           'otherfurniture', 'kitchen_cabinet', 'display', 'trash_bin', 'other_shelf', 'other_table']
                
                self.logger.info('Evaluate instance segmentation')
                scannet_eval = ScanNetEval(classes)
                eval_res_ap = scannet_eval.evaluate(pred_insts, gt_insts)
                
                # 更新评估结果 - 保存所有可能的详细结果
                eval_res.update({
                    'all_ap': eval_res_ap['all_ap'],
                    'all_ap_50%': eval_res_ap['all_ap_50%'],
                    'all_ap_25%': eval_res_ap['all_ap_25%']
                })
                
                # 如果有类别级别的AP结果，也保存
                if 'classes' in eval_res_ap:
                    eval_res['classes_ap'] = eval_res_ap['classes']
                if 'classes_ap_50%' in eval_res_ap:
                    eval_res['classes_ap_50%'] = eval_res_ap['classes_ap_50%']
                if 'classes_ap_25%' in eval_res_ap:
                    eval_res['classes_ap_25%'] = eval_res_ap['classes_ap_25%']
                
                # 记录类别名称用于wandb
                eval_res['class_names'] = classes
                
                self.logger.info('AP: {:.3f}. AP_50: {:.3f}. AP_25: {:.3f}'.format(
                    eval_res['all_ap'], eval_res['all_ap_50%'], eval_res['all_ap_25%']))
                
                # 记录详细的类别AP结果
                if 'classes_ap' in eval_res:
                    self.logger.info("Per-class AP:")
                    for i, (class_name, ap_val) in enumerate(zip(classes, eval_res['classes_ap'])):
                        self.logger.info(f"  {class_name}: {ap_val:.3f}")
                
            except Exception as e:
                self.logger.warning(f"AP evaluation failed: {e}")
        
        # 同步评估结果到所有进程
        if self.world_size > 1:
            # 广播AP结果从主进程到其他进程
            for key in ['all_ap', 'all_ap_50%', 'all_ap_25%']:
                value_tensor = torch.tensor(eval_res[key], device=self.device)
                dist.broadcast(value_tensor, src=0)
                eval_res[key] = value_tensor.item()
        
        return eval_res
    
    def _move_batch_to_device(self, batch):
        """将batch数据移动到设备"""
        for key, value in batch.items():
            if isinstance(value, torch.Tensor):
                batch[key] = value.to(self.device)
        return batch
    
    def _is_best_model(self, metrics):
        """判断是否是最佳模型（基于AP值）"""
        current_metric = metrics.get('all_ap', 0.0)
        return current_metric > self.best_metric
    
    def _is_best_val_loss(self, metrics):
        """判断是否是最低val loss的模型"""
        current_val_loss = metrics.get('val_loss', float('inf'))
        return current_val_loss < self.best_val_loss
    
    def _save_checkpoint(self, is_best=False, eval_metrics=None):
        """保存检查点"""
        if not is_main_process():
            return
        
        model_state_dict = self.model.module.state_dict() if hasattr(self.model, 'module') else self.model.state_dict()
        
        checkpoint = {
            'epoch': self.current_epoch,
            'model_state_dict': model_state_dict,
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict(),
            'best_metric': self.best_metric,
            'global_step': self.global_step,
            'config': self.config
        }
        
        # 保存定期检查点
        # checkpoint_path = os.path.join(self.exp_dir, f'checkpoint_epoch_{self.current_epoch}.pth')
        # torch.save(checkpoint, checkpoint_path)
        
        if is_best and eval_metrics:
            # 删除之前的最佳模型文件
            if hasattr(self, 'best_model_path') and os.path.exists(self.best_model_path):
                os.remove(self.best_model_path)
            
            # 保存新的最佳模型，文件名包含AP值
            ap = eval_metrics.get('all_ap', 0.0)
            ap_50 = eval_metrics.get('all_ap_50%', 0.0)
            ap_25 = eval_metrics.get('all_ap_25%', 0.0)
            
            best_filename = f'epoch{self.current_epoch:03d}_AP_{ap:.4f}_{ap_50:.4f}_{ap_25:.4f}.pth'
            self.best_model_path = os.path.join(self.exp_dir, best_filename)
            torch.save(checkpoint, self.best_model_path)
            
            # 注释：best_model.pth现在专门用于保存最低val loss的模型
            
            # 保存最佳模型到wandb (已禁用 - 模型文件太大)
            # try:
            #     wandb.save(self.best_model_path)
            #     wandb.save(best_link_path)
            #     self.logger.info(f"📤 Best model uploaded to wandb")
            # except Exception as e:
            #     self.logger.warning(f"⚠️ Failed to upload model to wandb: {e}")
            self.logger.info(f"💾 Best model saved locally (wandb upload disabled)")
            
            self.logger.info(f"🏆 Saved best model: {best_filename}")
            self.logger.info(f"    AP: {ap:.4f}, AP_50: {ap_50:.4f}, AP_25: {ap_25:.4f}")
            
            # 记录最佳模型信息到wandb
            wandb.log({
                'best_model/epoch': self.current_epoch,
                'best_model/ap': ap,
                'best_model/ap_50': ap_50,
                'best_model/ap_25': ap_25,
                'best_model/filename': best_filename
            }, step=self.global_step)
        elif is_best:
            # 如果没有评估指标，使用简单的best_model.pth
            best_path = os.path.join(self.exp_dir, 'best_model.pth')
            torch.save(checkpoint, best_path)
            self.logger.info(f"Saved best model at epoch {self.current_epoch}")
    
    def _save_latest_checkpoint(self):
        """保存最后一个epoch的模型为latest.pth"""
        if not is_main_process():
            return
        
        model_state_dict = self.model.module.state_dict() if hasattr(self.model, 'module') else self.model.state_dict()
        
        checkpoint = {
            'epoch': self.current_epoch,
            'model_state_dict': model_state_dict,
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict(),
            'best_metric': self.best_metric,
            'global_step': self.global_step,
            'config': self.config
        }
        
        # 保存为latest.pth
        latest_path = os.path.join(self.exp_dir, 'latest.pth')
        torch.save(checkpoint, latest_path)
        self.logger.info(f"💾 Saved latest model: latest.pth (epoch {self.current_epoch})")
    
    def _save_best_val_loss_checkpoint(self, eval_metrics):
        """保存最低val loss的模型为best_model.pth"""
        if not is_main_process():
            return
        
        model_state_dict = self.model.module.state_dict() if hasattr(self.model, 'module') else self.model.state_dict()
        
        checkpoint = {
            'epoch': self.current_epoch,
            'model_state_dict': model_state_dict,
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict(),
            'best_metric': self.best_metric,
            'best_val_loss': self.best_val_loss,
            'global_step': self.global_step,
            'config': self.config
        }
        
        # 删除之前的best_model.pth文件
        best_model_path = os.path.join(self.exp_dir, 'best_model.pth')
        if os.path.exists(best_model_path):
            os.remove(best_model_path)
        
        # 保存新的最低val loss模型
        torch.save(checkpoint, best_model_path)
        
        val_loss = eval_metrics.get('val_loss', float('inf'))
        self.logger.info(f"🏆 Saved best val loss model: best_model.pth (epoch {self.current_epoch}, val_loss: {val_loss:.4f})")
        
        # 记录最佳val loss模型信息到wandb
        wandb.log({
            'best_val_loss_model/epoch': self.current_epoch,
            'best_val_loss_model/val_loss': val_loss,
            'best_val_loss_model/ap': eval_metrics.get('all_ap', 0.0)
        }, step=self.global_step)
    
    def _resume_training(self, resume_path):
        """恢复训练"""
        if is_main_process():
            self.logger.info(f"Resuming training from {resume_path}")
        
        checkpoint = torch.load(resume_path, map_location=self.device, weights_only=False)
        
        if hasattr(self.model, 'module'):
            self.model.module.load_state_dict(checkpoint['model_state_dict'])
        else:
            self.model.load_state_dict(checkpoint['model_state_dict'])
        
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        self.scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
        
        self.current_epoch = checkpoint['epoch'] + 1
        self.best_metric = checkpoint['best_metric']
        
        # 恢复best_val_loss (如果checkpoint中有的话)
        if 'best_val_loss' in checkpoint:
            self.best_val_loss = checkpoint['best_val_loss']
        else:
            self.best_val_loss = float('inf')
        
        # 恢复global_step (如果checkpoint中有的话)
        if 'global_step' in checkpoint:
            self.global_step = checkpoint['global_step']
        else:
            # 估算global_step（基于已完成的epochs和平均batch数）
            self.global_step = self.current_epoch * len(self.train_loader)
        
        # 更新wandb配置以反映恢复的状态
        if is_main_process():
            wandb.config.update({
                'resumed_from_epoch': checkpoint['epoch'],
                'resumed_best_metric': checkpoint['best_metric'],
                'resumed_global_step': self.global_step,
                'resume_checkpoint': resume_path
            })
            self.logger.info(f"✅ 恢复训练: Epoch {self.current_epoch}, Best AP: {self.best_metric:.4f}, Global Step: {self.global_step}")
    
    def _log_epoch_metrics(self, train_metrics, val_metrics=None):
        """记录epoch指标"""
        epoch_time = time.time() - self.epoch_start_time if self.epoch_start_time else 0
        total_time = time.time() - self.train_start_time if self.train_start_time else 0
        
        total_epochs = self.config['train']['epochs']
        if self.current_epoch > 0:
            avg_epoch_time = total_time / (self.current_epoch + 1)
            remaining_epochs = total_epochs - self.current_epoch - 1
            eta_total = remaining_epochs * avg_epoch_time
        else:
            eta_total = 0
        
        mem_alloc, mem_reserved = get_gpu_memory()
        
        train_loss_str = f"Train Loss: {train_metrics['loss']:.4f}"
        if 'loss_components' in train_metrics and train_metrics['loss_components']:
            components = [f"{k}: {v:.3f}" for k, v in train_metrics['loss_components'].items()]
            train_loss_str += f" ({', '.join(components)})"
        
        progress = (self.current_epoch + 1) / total_epochs * 100
        
        self.logger.info("=" * 80)
        self.logger.info(
            f"🎯 EPOCH {self.current_epoch:03d}/{total_epochs:03d} COMPLETED "
            f"({progress:5.1f}%) | GPUs: {self.world_size}"
        )
        self.logger.info(f"⏱️  Time: {format_time(epoch_time)} (epoch) | {format_time(total_time)} (total)")
        
        if eta_total > 0:
            eta_finish = datetime.datetime.now() + datetime.timedelta(seconds=eta_total)
            self.logger.info(f"⏰ ETA: {format_time(eta_total)} | Finish: {eta_finish.strftime('%Y-%m-%d %H:%M:%S')}")
        
        self.logger.info(f"📊 {train_loss_str}")
        
        if val_metrics:
            val_loss_str = f"Val Loss: {val_metrics['val_loss']:.4f}"
            ap_str = f"AP: {val_metrics['all_ap']:.4f} | AP_50: {val_metrics['all_ap_50%']:.4f} | AP_25: {val_metrics['all_ap_25%']:.4f}"
            self.logger.info(f"📈 {val_loss_str} | {ap_str}")
            
            # 显示最佳模型更新信息
            if val_metrics['all_ap'] > self.best_metric:
                self.logger.info("🏆 NEW BEST AP MODEL!")
            if val_metrics['val_loss'] < self.best_val_loss:
                self.logger.info("🏆 NEW BEST VAL LOSS MODEL!")
        
        current_lr = self.optimizer.param_groups[0]['lr']
        self.logger.info(f"🔧 LR: {current_lr:.2e} | GPU{self.rank}: {mem_alloc:.1f}GB/{mem_reserved:.1f}GB")
        
        # 记录epoch级别的metrics到wandb
        epoch_metrics = {
            # 训练指标
            'epoch': self.current_epoch,
            'train/loss': train_metrics['loss'],
            'train/learning_rate': current_lr,
            
            # 时间指标
            'time/epoch_time': epoch_time,
            'time/total_time': total_time,
            'time/avg_epoch_time': total_time / (self.current_epoch + 1) if self.current_epoch > 0 else epoch_time,
            'time/eta_total': eta_total,
            'time/progress': progress,
            
            # 系统指标
            'system/gpu_memory_allocated': mem_alloc,
            'system/gpu_memory_reserved': mem_reserved,
            'system/best_metric': self.best_metric,
        }
        
        # 记录训练loss组件
        if 'loss_components' in train_metrics and train_metrics['loss_components']:
            for key, value in train_metrics['loss_components'].items():
                epoch_metrics[f'train/loss_{key}'] = value
        
                 # 记录验证指标
        if val_metrics:
            epoch_metrics.update({
                'val/loss': val_metrics['val_loss'],
                'val/all_ap': val_metrics['all_ap'],
                'val/all_ap_50': val_metrics['all_ap_50%'],
                'val/all_ap_25': val_metrics['all_ap_25%'],
                'val/is_best': val_metrics['all_ap'] > self.best_metric,
            })
            
            # 记录验证loss组件
            if 'loss_components' in val_metrics and val_metrics['loss_components']:
                for key, value in val_metrics['loss_components'].items():
                    epoch_metrics[f'val/loss_{key}'] = value
            
            # 记录详细的类别AP结果
            if 'classes_ap' in val_metrics and 'class_names' in val_metrics:
                class_names = val_metrics['class_names']
                for i, (class_name, ap_val) in enumerate(zip(class_names, val_metrics['classes_ap'])):
                    epoch_metrics[f'val/class_ap/{class_name}'] = ap_val
                
                # AP_50和AP_25的类别结果
                if 'classes_ap_50%' in val_metrics:
                    for i, (class_name, ap_val) in enumerate(zip(class_names, val_metrics['classes_ap_50%'])):
                        epoch_metrics[f'val/class_ap_50/{class_name}'] = ap_val
                
                if 'classes_ap_25%' in val_metrics:
                    for i, (class_name, ap_val) in enumerate(zip(class_names, val_metrics['classes_ap_25%'])):
                        epoch_metrics[f'val/class_ap_25/{class_name}'] = ap_val
            
            # 其他可能的评估指标
            for key, value in val_metrics.items():
                if key.startswith('classes_') and key not in ['classes_ap', 'classes_ap_50%', 'classes_ap_25%']:
                    epoch_metrics[f'val/{key}'] = value
        
        # 如果有batch时间统计，记录平均值
        if self.batch_times:
            recent_batch_times = self.batch_times[-100:]  # 最近100个batch
            epoch_metrics.update({
                'time/avg_batch_time': np.mean(recent_batch_times),
                'time/batch_time_std': np.std(recent_batch_times),
                'time/max_batch_time': np.max(recent_batch_times),
                'time/min_batch_time': np.min(recent_batch_times),
            })
        
        wandb.log(epoch_metrics, step=self.global_step)
        
        # 记录学习率曲线
        wandb.log({'lr_curve': current_lr}, step=self.global_step)

    def _init_wandb(self):
        """初始化wandb"""
        # 获取模型参数统计
        model_config = self.config['model'].copy()
        train_config = self.config['train'].copy()
        
        # wandb配置
        wandb_config = {
            # 模型配置
            'model': model_config,
            'architecture': 'MAFT',
            
            # 训练配置
            'epochs': train_config.get('epochs', 100),
            'batch_size': self.config['dataloader']['train']['batch_size'],
            'effective_batch_size': self.config['dataloader']['train']['batch_size'] * self.world_size,
            'learning_rate': self.config['optimizer']['lr'],
            'weight_decay': self.config['optimizer']['weight_decay'],
            'optimizer': self.config['optimizer']['type'],
            'scheduler': self.config['lr_scheduler']['type'],
            'seed': train_config.get('seed', 42),
            
            # 系统配置
            'world_size': self.world_size,
            'device': str(self.device),
            'cuda_version': torch.version.cuda if torch.cuda.is_available() else 'N/A',
            'pytorch_version': torch.__version__,
            
            # 数据配置
            'dataset': self.config.get('data', {}),
            'dataloader': self.config.get('dataloader', {}),
        }
        
        # 初始化wandb项目
        wandb.init(
            project="MAFT-Training",  # 项目名称
            name=self.exp_name,       # 实验名称
            config=wandb_config,
            dir=self.exp_dir,
            resume="allow" if os.path.exists(os.path.join(self.exp_dir, 'wandb')) else None,
            tags=['distributed', 'instance_segmentation', 'maft'],  # 标签
            notes=f""  # 描述
        )
        
        # 上传配置文件到wandb
        try:
            if os.path.exists(self.config_path):
                wandb.save(self.config_path, base_path=os.path.dirname(self.config_path))
                self.logger.info(f"✅ 配置文件已上传到wandb: {self.config_path}")
            else:
                self.logger.warning(f"⚠️ 配置文件不存在，无法上传: {self.config_path}")
        except Exception as e:
            self.logger.warning(f"⚠️ 上传配置文件失败: {e}")
        
        self.logger.info(f"🌟 Wandb initialized: {wandb.run.name}")
        self.logger.info(f"🔗 Wandb URL: {wandb.run.url}")
    
    def _create_training_summary(self):
        """创建训练总结"""
        total_time = time.time() - self.train_start_time if self.train_start_time else 0
        
        # 创建总结表格
        summary_data = [
            ["Metric", "Value"],
            ["🏆 Best AP", f"{self.best_metric:.4f}"],
            ["📊 Total Epochs", f"{self.current_epoch + 1}"],
            ["⏱️ Total Time", format_time(total_time)],
            ["💾 World Size", f"{self.world_size}"],
            ["🔢 Batch Size", f"{self.config['dataloader']['train']['batch_size']}"],
            ["🔢 Effective Batch Size", f"{self.config['dataloader']['train']['batch_size'] * self.world_size}"],
            ["📚 Train Samples", f"{len(self.train_loader.dataset):,}"],
            ["📚 Val Samples", f"{len(self.val_loader.dataset):,}"],
        ]
        
        # 记录到wandb
        wandb.log({"training_summary": wandb.Table(data=summary_data, columns=["Metric", "Value"])})
        
        # 记录最终指标
        final_metrics = {
            "final/best_ap": self.best_metric,
            "final/total_epochs": self.current_epoch + 1,
            "final/total_time_hours": total_time / 3600,
            "final/avg_epoch_time_minutes": (total_time / (self.current_epoch + 1)) / 60,
        }
        
        wandb.log(final_metrics)
        
        self.logger.info("📋 Training Summary:")
        for row in summary_data[1:]:  # 跳过表头
            self.logger.info(f"   {row[0]}: {row[1]}")
        
        self.logger.info(f"🎉 实验完成! 最佳AP: {self.best_metric:.4f}")
        self.logger.info(f"📊 wandb链接: {wandb.run.url}")


def run_worker(rank, world_size, config_path, exp_name, resume):
    """分布式训练工作进程"""
    try:
        setup_distributed(rank, world_size)
        
        trainer = MAFTTrainerDDP(
            config_path=config_path,
            exp_name=exp_name,
            resume=resume,
            rank=rank,
            world_size=world_size
        )
        
        trainer.train()
        
    except Exception as e:
        print(f"Error in rank {rank}: {e}")
        import traceback
        traceback.print_exc()
    finally:
        cleanup_distributed()


def main():
    parser = argparse.ArgumentParser(description='MAFT Distributed Training')
    parser.add_argument('--config', type=str, required=True, help='Path to config file')
    parser.add_argument('--exp-name', type=str, help='Experiment name')
    parser.add_argument('--resume', type=str, help='Path to checkpoint to resume from')
    parser.add_argument('--world-size', type=int, default=2, help='Number of GPUs to use')
    
    args = parser.parse_args()
    
    if not torch.cuda.is_available():
        raise RuntimeError("CUDA is not available")
    
    if torch.cuda.device_count() < args.world_size:
        raise RuntimeError(f"Need {args.world_size} GPUs, but only {torch.cuda.device_count()} available")
    
    print(f"启动分布式训练，使用 {args.world_size} 块GPU")
    
    if args.world_size == 1:
        trainer = MAFTTrainerDDP(
            config_path=args.config,
            exp_name=args.exp_name,
            resume=args.resume,
            rank=0,
            world_size=1
        )
        trainer.train()
    else:
        import torch.multiprocessing as mp
        mp.spawn(
            run_worker,
            args=(args.world_size, args.config, args.exp_name, args.resume),
            nprocs=args.world_size,
            join=True
        )


if __name__ == '__main__':
    main()